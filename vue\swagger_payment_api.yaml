openapi: 3.0.0
info:
  title: FinCore 支付管理 API
  description: 支付管理控制器的完整API接口文档，包括代付回调通知等功能
  version: 1.0.0
  contact:
    name: FinCore Team
    email: <EMAIL>

servers:
  - url: http://localhost:8108
    description: 支付管理API服务器（开发环境）
  - url: https://api.fincore.com
    description: 支付管理API服务器（生产环境）

components:
  schemas:
    RefundRequest:
      type: object
      description: 退款请求参数
      properties:
        transaction_id:
          type: integer
          description: 流水记录ID
          minimum: 1
          example: 12345
        refund_amount:
          type: number
          format: float
          description: 退款金额
          minimum: 0.01
          example: 1000.50
        refund_reason:
          type: string
          description: 退款原因
          maxLength: 500
          example: "客户申请退款，原因：重复支付"
      required:
        - transaction_id
        - refund_amount
        - refund_reason

    RefundResponse:
      type: object
      description: 退款响应结果
      properties:
        refund_transaction_no:
          type: string
          description: 退款流水号
          example: "REF20241201001"
        refund_amount:
          type: number
          format: float
          description: 退款金额
          example: 1000.50
        status:
          type: string
          description: 退款状态
          enum: ["已提交", "处理中", "成功", "失败"]
          example: "已提交"
        message:
          type: string
          description: 状态描述
          example: "退款请求已提交，等待第三方处理"

    RefreshRefundStatusRequest:
      type: object
      description: 刷新退款状态请求参数
      properties:
        transaction_id:
          type: integer
          description: 流水记录ID
          minimum: 1
          example: 12345
      required:
        - transaction_id

    DisbursementCallbackRequest:
      type: object
      description: 代付回调通知请求参数
      properties:
        resp_code:
          type: string
          description: 响应码 成功返回 000000
          example: "000000"
        resp_msg:
          type: string
          description: 响应码不为 0 返回失败原因
          example: "成功"
        sign_type:
          type: string
          description: 签名类型 支持CERT和RSA必须大写,API接入方式使用CERT
          example: "CERT"
        sign:
          type: string
          description: 签名
          example: "ABC123..."
        order_no:
          type: string
          description: 订单号
          example: "ORD20241201001"
        trace_no:
          type: string
          description: 交易流水号
          example: "TXN20241201001"
        order_amount:
          type: string
          description: 订单金额 保留两位小数
          example: "5000.00"
        status:
          type: string
          description: 状态 00 成功，01 处理中，03 失败
          enum: ["00", "01", "03"]
          example: "00"
        fee:
          type: string
          description: 手续费 保留两位小数
          example: "5.00"
          required: false
        obo_type:
          type: string
          description: 业务类型 01 代付，02 代收
          example: "01"
          required: false
        remark:
          type: string
          description: 商户请求传入的remark, 原样返回
          example: "代付备注"
          required: false
        ttf_return_code:
          type: string
          description: 失败时可能会返回
          example: "E001"
          required: false
        ttf_return_msg:
          type: string
          description: 失败时可能会返回
          example: "余额不足"
          required: false
      required:
        - resp_code
        - resp_msg
        - sign_type
        - sign
        - order_no
        - trace_no
        - order_amount
        - status

    DisbursementCallbackResponse:
      type: object
      description: 代付回调通知响应
      properties:
        resp_code:
          type: string
          description: 响应码 成功返回 000000，失败返回其他
          example: "000000"
        resp_msg:
          type: string
          description: 响应信息
          example: "处理成功"
      required:
        - resp_code
        - resp_msg

    PartialOfflinePaymentRequest:
      type: object
      description: 部分线下支付请求参数
      properties:
        bill_id:
          type: integer
          description: 账单ID
          minimum: 1
          example: 12345
        payment_channel:
          type: string
          description: 支付渠道文案
          maxLength: 20
          example: "银行转账"
        voucher:
          type: string
          description: 凭证图片
          maxLength: 500
          example: "http://example.com/voucher.jpg"
        amount:
          type: number
          format: float
          description: 支付金额
          minimum: 0.01
          maximum: 999999.99
          example: 1000.50
      required:
        - bill_id
        - payment_channel
        - voucher
        - amount

    PartialOfflinePaymentResponse:
      type: object
      description: 部分线下支付响应结果
      properties:
        transaction_no:
          type: string
          description: 生成的流水号
          example: "POF1703123456789"
        bill_id:
          type: integer
          description: 账单ID
          example: 12345
        amount:
          type: number
          format: float
          description: 支付金额
          example: 1000.50
        status:
          type: string
          description: 处理状态
          example: "success"
        message:
          type: string
          description: 状态描述
          example: "部分线下支付处理成功"

    CancelOfflinePaymentRequest:
      type: object
      description: 销账取消请求参数
      properties:
        transaction_no:
          type: string
          description: 流水号
          minLength: 1
          maxLength: 50
          example: "TXN20241201001"
      required:
        - transaction_no

    CancelOfflinePaymentResponse:
      type: object
      description: 销账取消响应结果
      properties:
        transaction_no:
          type: string
          description: 取消的流水号
          example: "TXN20241201001"
        bill_id:
          type: integer
          description: 账单ID
          example: 12345
        amount:
          type: number
          format: float
          description: 取消金额
          example: 500.00
        status:
          type: string
          description: 处理状态
          example: "success"
        message:
          type: string
          description: 处理消息
          example: "销账取消处理成功"

    CalculateScheduleRequest:
      type: object
      description: 计算还款计划请求参数
      properties:
        loan_amount:
          type: number
          format: float
          description: 贷款金额
          minimum: 0.01
          maximum: 10000000
          example: 10000.00
        loan_period:
          type: integer
          description: 借款周期天数
          minimum: 1
          maximum: 3650
          example: 30
        total_periods:
          type: integer
          description: 总期数
          minimum: 1
          maximum: 120
          example: 3
        guarantee_fee:
          type: number
          format: float
          description: 担保费
          minimum: 0
          maximum: 1000000
          example: 500.00
        annual_interest_rate:
          type: number
          format: float
          description: 年利率（百分比）
          minimum: 0
          maximum: 100
          example: 15.0
        other_fees:
          type: number
          format: float
          description: 其他费用
          minimum: 0
          maximum: 1000000
          example: 100.00
        is_pre_payment:
          type: boolean
          description: 是否前置放款
          example: false
      required:
        - loan_amount
        - loan_period
        - total_periods
        - guarantee_fee
        - annual_interest_rate
        - other_fees
        - is_pre_payment

    RepaymentPeriod:
      type: object
      description: 单期还款计划
      properties:
        period_number:
          type: integer
          description: 期数
          example: 1
        due_principal:
          type: number
          format: float
          description: 应还本金
          example: 3333.33
        due_interest:
          type: number
          format: float
          description: 应还利息
          example: 41.10
        due_guarantee_fee:
          type: number
          format: float
          description: 应还担保费
          example: 166.67
        due_other_fees:
          type: number
          format: float
          description: 应还其他费用
          example: 33.33
        asset_management_fee:
          type: number
          format: float
          description: 资管费
          example: 3407.76
        late_fee:
          type: number
          format: float
          description: 逾期费
          example: 0
        total_due_amount:
          type: number
          format: float
          description: 当期应还总额
          example: 3574.43
        due_date:
          type: string
          format: date
          description: 到期日期
          example: "2025-08-28"

    RepaymentSchedule:
      type: object
      description: 完整还款计划
      properties:
        total_periods:
          type: integer
          description: 总期数
          example: 3
        periods:
          type: array
          description: 各期还款计划
          items:
            $ref: '#/components/schemas/RepaymentPeriod'
        total_principal:
          type: number
          format: float
          description: 总本金
          example: 10000.00
        total_interest:
          type: number
          format: float
          description: 总利息
          example: 123.29
        total_guarantee_fee:
          type: number
          format: float
          description: 总担保费
          example: 500.00
        total_other_fees:
          type: number
          format: float
          description: 总其他费用
          example: 100.00
        total_asset_management_fee:
          type: number
          format: float
          description: 总资管费
          example: 10223.29
        total_repayable_amount:
          type: number
          format: float
          description: 总应还金额
          example: 10723.29
        disbursement_amount:
          type: number
          format: float
          description: 实际放款金额
          example: 10000.00
        is_pre_payment:
          type: boolean
          description: 是否为前置还款
          example: false

    MerchantBalance:
      type: object
      description: 单个商户余额信息
      properties:
        mer_no:
          type: string
          description: 商户号
          example: "M001"
        merchant_name:
          type: string
          description: 商户名称（公司名称）
          example: "武汉市几何资产投资管理有限公司"
        merchant_type:
          type: string
          description: 商户类型
          enum: ["资管公司", "担保公司", "小贷公司"]
          example: "资管公司"
        platform_name:
          type: string
          description: 平台名称
          example: "TTF"
        total_balance:
          type: string
          description: 总余额
          example: "100000.00"
        pending_conversion_amount:
          type: string
          description: 待转化额度
          example: "5000.00"
        withdrawable_amount:
          type: string
          description: 可提现额度
          example: "95000.00"
        overdue_amount:
          type: string
          description: 累计逾期金额（仅资管、担保商户，小贷商户为0）
          example: "2500.00"
        future_receivable_amount:
          type: string
          description: 未来应收金额（仅资管、担保商户，小贷商户为0）
          example: "15000.00"

    MerchantsBalanceResponse:
      type: object
      description: 商户余额查询响应
      properties:
        merchants_balance:
          type: array
          description: 各商户余额详细信息
          items:
            $ref: '#/components/schemas/MerchantBalance'
        total_balance:
          type: string
          description: 账户总额度（所有商户余额汇总）
          example: "230000.00"
        pending_conversion_amount:
          type: string
          description: 待转化总额度（所有商户待转化额度汇总）
          example: "10000.00"
        withdrawable_amount:
          type: string
          description: 可提现总额度（所有商户可提现额度汇总）
          example: "220000.00"
        overdue_amount:
          type: string
          description: 累计逾期总额度（资管和担保商户逾期金额汇总）
          example: "4300.00"
        future_receivable_amount:
          type: string
          description: 未来应收总额度（资管和担保商户未来应收金额汇总）
          example: "27000.00"

paths:
  /business/payment/manager/getMerchantsBalance:
    get:
      summary: 查询商户余额
      description: |
        查询所有商户的余额信息，包括各商户详细信息和汇总数据。

        **功能说明：**
        1. 并发查询第三方支付平台获取商户余额信息
        2. 计算商户累计逾期金额（仅资管、担保商户）
        3. 计算商户未来应收金额（仅资管、担保商户）
        4. 返回三个商户的详细余额信息和汇总数据

        **商户类型：**
        - 资管公司：武汉市几何资产投资管理有限公司
        - 担保公司：武汉盛唐融资担保有限公司
        - 小贷公司：武汉市几何资产投资管理有限公司

        **累计逾期金额计算规则：**
        - 筛选条件：账单状态为逾期待支付(3)和逾期部分支付(9)，且订单状态为放款中(1)
        - 计算逻辑：按比例分配已还金额到担保费和资管费，计算各自的逾期金额
        - 小贷商户的逾期金额固定为 0.00

        **未来应收金额计算规则：**
        - 筛选条件：账单状态为非完结状态(0,3,7,9)，且订单状态为放款中(1)
        - 计算逻辑：按比例分配已还金额到担保费和资管费，计算各自的未来应收金额
        - 小贷商户的未来应收金额固定为 0.00

        **汇总数据：**
        - 总余额：所有商户余额的汇总
        - 待转化总额度：所有商户待转化额度的汇总
        - 可提现总额度：所有商户可提现额度的汇总
        - 累计逾期总额度：资管和担保商户逾期金额的汇总
        - 未来应收总额度：资管和担保商户未来应收金额的汇总

        **注意事项：**
        - 该接口需要身份验证，只有授权用户可以访问
        - 使用并发处理提高查询性能
        - 所有金额保留两位小数
        - 第三方支付接口查询超时时间为15秒
      tags:
        - 支付管理
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 查询商户余额成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 业务状态码，0表示成功，1表示失败
                    example: 0
                  message:
                    type: string
                    description: 响应消息
                    example: "查询商户余额成功"
                  data:
                    $ref: '#/components/schemas/MerchantsBalanceResponse'
                  time:
                    type: integer
                    description: 响应时间戳
                    example: 1701234567
              examples:
                success:
                  summary: 查询成功
                  value:
                    code: 0
                    message: "商户余额信息查询成功"
                    data:
                      merchants_balance:
                        - mer_no: "M001"
                          merchant_name: "武汉市几何资产投资管理有限公司"
                          merchant_type: "资管公司"
                          platform_name: "TTF"
                          total_balance: "100000.00"
                          pending_conversion_amount: "5000.00"
                          withdrawable_amount: "95000.00"
                          overdue_amount: "2500.00"
                          future_receivable_amount: "15000.00"
                        - mer_no: "M002"
                          merchant_name: "武汉盛唐融资担保有限公司"
                          merchant_type: "担保公司"
                          platform_name: "TTF"
                          total_balance: "80000.00"
                          pending_conversion_amount: "3000.00"
                          withdrawable_amount: "77000.00"
                          overdue_amount: "1800.00"
                          future_receivable_amount: "12000.00"
                        - mer_no: "M003"
                          merchant_name: "武汉市几何资产投资管理有限公司"
                          merchant_type: "小贷公司"
                          platform_name: "TTF"
                          total_balance: "50000.00"
                          pending_conversion_amount: "2000.00"
                          withdrawable_amount: "48000.00"
                          overdue_amount: "0.00"
                          future_receivable_amount: "0.00"
                      total_balance: "230000.00"
                      pending_conversion_amount: "10000.00"
                      withdrawable_amount: "220000.00"
                      overdue_amount: "4300.00"
                      future_receivable_amount: "27000.00"
                    time: 1701234567
                unauthorized:
                  summary: 未授权访问
                  value:
                    code: 1
                    message: "用户信息获取失败"
                    data: null
                    time: 1701234567
                third_party_error:
                  summary: 第三方接口调用失败
                  value:
                    code: 1
                    message: "商户余额查询失败: 网络超时"
                    data: null
                    time: 1701234567
                calculation_error:
                  summary: 金额计算失败
                  value:
                    code: 1
                    message: "商户余额信息查询失败: 查询账单数据失败"
                    data: null
                    time: 1701234567
                service_error:
                  summary: 服务内部错误
                  value:
                    code: 1
                    message: "商户余额信息查询失败: 获取支付服务失败"
                    data: null
                    time: 1701234567

  /business/payment/manager/calculateRepaymentSchedule:
    post:
      summary: 计算还款计划
      description: |
        根据输入的贷款参数实时计算完整的还款计划。

        **功能说明：**
        1. 接收贷款金额、借款周期、期数、担保费、年利率、其他费用、是否前置放款等参数
        2. 调用还款计算器进行实时计算
        3. 返回完整的还款计划，包括各期详细信息和汇总数据
        4. 支持前置付款和后置付款两种模式

        **计算逻辑：**
        - 利息计算：贷款金额 × 年利率 × 借款周期天数 / 365
        - 各项费用按期数均摊到每期
        - 前置付款：实际放款金额 = 贷款金额 - 利息 - 担保费 - 其他费用
        - 后置付款：实际放款金额 = 贷款金额

        **业务规则：**
        - 所有参数均为必填项
        - 贷款金额范围：0.01 - 10,000,000
        - 借款周期范围：1 - 3650 天
        - 总期数范围：1 - 120 期
        - 年利率范围：0 - 100%
        - 担保费和其他费用范围：0 - 1,000,000

        **注意事项：**
        - 该接口为纯计算接口，不涉及数据库操作
        - 计算结果仅供参考，实际放款以订单创建时的计算为准
        - 所有金额保留两位小数
      tags:
        - 支付管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CalculateScheduleRequest'
            example:
              loan_amount: 10000.00
              loan_period: 30
              total_periods: 3
              guarantee_fee: 500.00
              annual_interest_rate: 15.0
              other_fees: 100.00
              is_pre_payment: false
      responses:
        '200':
          description: 计算还款计划处理完成
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 业务状态码，0表示成功，1表示失败
                    example: 0
                  message:
                    type: string
                    description: 响应消息
                    example: "计算还款计划成功"
                  data:
                    $ref: '#/components/schemas/RepaymentSchedule'
                  time:
                    type: integer
                    description: 响应时间戳
                    example: 1701234567
              examples:
                success:
                  summary: 计算成功
                  value:
                    code: 0
                    message: "计算还款计划成功"
                    data:
                      total_periods: 3
                      total_principal: 10000.00
                      total_interest: 123.29
                      total_guarantee_fee: 500.00
                      total_other_fees: 100.00
                      total_asset_management_fee: 10223.29
                      total_repayable_amount: 10723.29
                      disbursement_amount: 10000.00
                      is_pre_payment: false
                      periods:
                        - period_number: 1
                          due_principal: 3333.33
                          due_interest: 41.10
                          due_guarantee_fee: 166.67
                          due_other_fees: 33.33
                          asset_management_fee: 3407.76
                          late_fee: 0
                          total_due_amount: 3574.43
                          due_date: "2025-08-28"
                        - period_number: 2
                          due_principal: 3333.33
                          due_interest: 41.10
                          due_guarantee_fee: 166.67
                          due_other_fees: 33.33
                          asset_management_fee: 3407.76
                          late_fee: 0
                          total_due_amount: 3574.43
                          due_date: "2025-09-27"
                        - period_number: 3
                          due_principal: 3333.33
                          due_interest: 41.10
                          due_guarantee_fee: 166.67
                          due_other_fees: 33.33
                          asset_management_fee: 3407.76
                          late_fee: 0
                          total_due_amount: 3574.43
                          due_date: "2025-10-27"
                    time: 1701234567
                invalid_params:
                  summary: 参数验证失败
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: null
                    time: 1701234567
                invalid_amount:
                  summary: 贷款金额无效
                  value:
                    code: 1
                    message: "计算还款计划失败: 产品规则中的贷款金额必须大于0"
                    data: null
                    time: 1701234567
                calculation_error:
                  summary: 计算失败
                  value:
                    code: 1
                    message: "计算还款计划失败: 参数错误"
                    data: null
                    time: 1701234567

  /business/payment/manager/processRefund:
    post:
      summary: 客户退款
      description: |
        处理客户退款请求，支持对成功的支付流水进行退款操作。

        **功能说明：**
        1. 验证流水记录ID、退款金额和退款原因的有效性
        2. 查询原始流水记录，验证流水状态必须为成功状态
        3. 验证退款金额不能超过原交易金额
        4. 验证累计退款金额不能超过账单当期应还总额
        5. 使用分布式锁防止并发退款操作
        6. 创建退款流水记录并存储退款原因
        7. 调用第三方退款接口
        8. 记录详细的操作日志便于追踪

        **业务规则：**
        - 只能对处理成功的流水进行退款
        - 退款金额必须大于0.01且不超过原交易金额
        - 累计退款金额不能超过账单当期应还总额
        - 退款原因为必填项，最大长度500字符
        - 同一流水记录同时只能有一个退款操作
        - 退款操作会记录操作员信息和详细日志
        - 退款成功后会更新账单的累计退款金额

        **退款状态说明：**
        - "已提交"：退款请求已提交给第三方，等待处理
        - "处理中"：第三方正在处理退款
        - "成功"：退款成功，资金已退回
        - "失败"：退款失败，需要人工处理

        **注意事项：**
        - 该接口需要身份验证，只有授权用户可以操作
        - 退款操作不可逆，请谨慎操作
        - 退款结果通过异步回调更新，可通过查询接口获取最新状态
      tags:
        - 支付管理
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefundRequest'
            example:
              transaction_id: 12345
              refund_amount: 1000.50
              refund_reason: "客户申请退款，原因：重复支付"
      responses:
        '200':
          description: 退款请求处理完成
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 业务状态码，0表示成功，1表示失败
                    example: 0
                  message:
                    type: string
                    description: 响应消息
                    example: "退款处理成功"
                  data:
                    $ref: '#/components/schemas/RefundResponse'
                  time:
                    type: integer
                    description: 响应时间戳
                    example: 1701234567
              examples:
                success:
                  summary: 退款提交成功
                  value:
                    code: 0
                    message: "退款处理成功"
                    data:
                      refund_transaction_no: "REF20241201001"
                      refund_amount: 1000.50
                      status: "已提交"
                      message: "退款请求已提交，等待第三方处理"
                    time: 1701234567
                invalid_params:
                  summary: 参数验证失败
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: null
                    time: 1701234567
                invalid_amount:
                  summary: 退款金额无效
                  value:
                    code: 1
                    message: "退款金额必须大于0"
                    data: null
                    time: 1701234567
                unauthorized:
                  summary: 未授权访问
                  value:
                    code: 1
                    message: "用户信息获取失败"
                    data: null
                    time: 1701234567
                server_error:
                  summary: 服务器内部错误
                  value:
                    code: 1
                    message: "退款处理失败"
                    data: null
                    time: 1701234567
                transaction_not_found:
                  summary: 流水记录不存在
                  value:
                    code: 1
                    message: "查询流水记录失败: 记录不存在"
                    data: null
                    time: 1701234567
                invalid_status:
                  summary: 流水状态不允许退款
                  value:
                    code: 1
                    message: "只能对处理成功的流水进行退款，当前状态: 1"
                    data: null
                    time: 1701234567
                amount_exceeded:
                  summary: 退款金额超限
                  value:
                    code: 1
                    message: "退款金额不能超过原交易金额"
                    data: null
                    time: 1701234567
                third_party_error:
                  summary: 第三方接口调用失败
                  value:
                    code: 1
                    message: "调用退款接口失败: 网络超时"
                    data: null
                    time: 1701234567

  /business/payment/manager/refreshRefundStatus:
    post:
      summary: 刷新退款状态
      description: |
        主动查询第三方退款状态并更新本地记录。

        **功能说明：**
        1. 验证流水记录ID的有效性
        2. 查询退款流水记录，验证记录存在
        3. 校验流水状态必须为"已提交"状态
        4. 调用第三方退款查询接口获取最新状态
        5. 根据查询结果更新本地流水状态
        6. 记录详细的操作日志便于追踪

        **业务规则：**
        - 只能查询状态为"已提交"的退款流水
        - 查询成功后会自动更新本地流水状态
        - 支持幂等性操作，可重复调用
        - 查询失败时会记录详细错误信息

        **状态更新逻辑：**
        - 第三方返回成功：更新为"成功"状态
        - 第三方返回失败：更新为"失败"状态
        - 第三方返回处理中：更新为"处理中"状态
        - 查询接口调用失败：保持原状态，记录错误

        **注意事项：**
        - 该接口需要身份验证，只有授权用户可以操作
        - 建议在退款提交后一段时间再调用此接口查询状态
        - 频繁调用可能会被第三方限流，建议合理控制调用频率
      tags:
        - 支付管理
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshRefundStatusRequest'
            example:
              transaction_id: 12345
      responses:
        '200':
          description: 刷新退款状态处理完成
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 业务状态码，0表示成功，1表示失败
                    example: 0
                  message:
                    type: string
                    description: 响应消息
                    example: "刷新退款状态成功"
                  data:
                    type: object
                    nullable: true
                    description: 响应数据，刷新操作无返回数据
                    example: null
                  time:
                    type: integer
                    description: 响应时间戳
                    example: 1701234567
              examples:
                success:
                  summary: 刷新成功
                  value:
                    code: 0
                    message: "刷新退款状态成功"
                    data: null
                    time: 1701234567
                invalid_params:
                  summary: 参数验证失败
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: null
                    time: 1701234567
                invalid_format:
                  summary: 参数格式错误
                  value:
                    code: 1
                    message: "参数格式错误"
                    data: null
                    time: 1701234567
                invalid_transaction_id:
                  summary: 流水记录ID无效
                  value:
                    code: 1
                    message: "流水记录ID必须大于0"
                    data: null
                    time: 1701234567
                unauthorized:
                  summary: 未授权访问
                  value:
                    code: 1
                    message: "用户信息获取失败"
                    data: null
                    time: 1701234567
                transaction_not_found:
                  summary: 流水记录不存在
                  value:
                    code: 1
                    message: "查询退款流水记录失败: 记录不存在"
                    data: null
                    time: 1701234567
                invalid_status:
                  summary: 流水状态不允许查询
                  value:
                    code: 1
                    message: "退款流水状态不是已提交，当前状态: 2"
                    data: null
                    time: 1701234567
                service_error:
                  summary: 获取支付服务失败
                  value:
                    code: 1
                    message: "获取支付服务失败: 配置错误"
                    data: null
                    time: 1701234567
                third_party_error:
                  summary: 第三方接口调用失败
                  value:
                    code: 1
                    message: "调用退款查询接口失败: 网络超时"
                    data: null
                    time: 1701234567
                response_format_error:
                  summary: 第三方响应格式异常
                  value:
                    code: 1
                    message: "第三方响应格式异常：缺少resp_code字段"
                    data: null
                    time: 1701234567

  /business/payment/manager/disbursementCallback:
    post:
      summary: 代付回调通知
      description: |
        处理第三方支付平台的代付回调通知。
        
        **功能说明：**
        1. 根据交易流水号查询 business_payment_transactions 表中对应记录
        2. 更新交易记录的状态、结果等数据
        3. 当 status 字段为 "00" 时，将订单表对应记录变更为放款中状态
        4. 支持幂等性处理，避免重复处理同一回调
        
        **状态映射：**
        - "00" (成功) -> 交易状态：成功，订单状态：放款中
        - "01" (处理中) -> 交易状态：处理中
        - "03" (失败) -> 交易状态：失败
        
        **注意事项：**
        - 该接口为第三方回调接口，无需身份验证
        - 响应格式固定为 HTTP 200，业务成功失败通过 resp_code 区分
        - 只有 resp_code 为 "000000" 表示成功，其他均为失败
      tags:
        - 支付管理
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DisbursementCallbackRequest'
            example:
              resp_code: "000000"
              resp_msg: "成功"
              sign_type: "CERT"
              sign: "ABC123DEF456..."
              order_no: "ORD20241201001"
              trace_no: "TXN20241201001"
              order_amount: "5000.00"
              status: "00"
              fee: "5.00"
              obo_type: "01"
              remark: "代付备注"
      responses:
        '200':
          description: 回调处理完成（无论成功失败都返回200）
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DisbursementCallbackResponse'
              examples:
                success:
                  summary: 处理成功
                  value:
                    resp_code: "000000"
                    resp_msg: "处理成功"
                failure:
                  summary: 处理失败
                  value:
                    resp_code: "000001"
                    resp_msg: "查询交易记录失败: 交易流水号不存在"

  /business/payment/manager/processPartialOfflinePayment:
    post:
      summary: 部分线下支付
      description: |
        处理用户的部分线下还款支付。

        **功能说明：**
        1. 验证账单状态和支付金额的有效性
        2. 创建线下部分还款类型的支付流水记录
        3. 更新账单的已还金额和状态
        4. 更新订单的已还金额，如全部还清则更新订单状态
        5. 记录详细的操作日志便于追踪

        **业务规则：**
        - 只允许对"待支付"、"逾期待支付"、"部分还款"状态的账单进行操作
        - 支付金额不能超过剩余应还金额
        - 支付成功后直接设置为成功状态，不调用第三方接口
        - 使用账单维度内存锁防止并发操作

        **状态更新逻辑：**
        - 部分还款：账单状态 -> "部分还款"(7)
        - 全额还清：账单状态 -> "已支付"(1) 或 "逾期已支付"(2)
        - 订单结清：当所有账单都已还清时，订单状态 -> "已结清"(3)

        **注意事项：**
        - 该接口需要身份验证，只有授权用户可以操作
        - 支付操作会记录操作员信息和详细日志
        - 支付流水直接设置为成功状态，无需等待第三方回调
      tags:
        - 支付管理
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PartialOfflinePaymentRequest'
            example:
              bill_id: 12345
              payment_channel: "银行转账"
              voucher: "http://example.com/voucher.jpg"
              amount: 1000.50
      responses:
        '200':
          description: 部分线下支付处理完成
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 业务状态码，0表示成功，1表示失败
                    example: 0
                  message:
                    type: string
                    description: 响应消息
                    example: "部分线下支付处理成功"
                  data:
                    $ref: '#/components/schemas/PartialOfflinePaymentResponse'
                  time:
                    type: integer
                    description: 响应时间戳
                    example: 1701234567
              examples:
                success:
                  summary: 部分线下支付成功
                  value:
                    code: 0
                    message: "部分线下支付处理成功"
                    data:
                      transaction_no: "POF1703123456789"
                      bill_id: 12345
                      amount: 1000.50
                      status: "success"
                      message: "部分线下支付处理成功"
                    time: 1701234567
                invalid_params:
                  summary: 参数验证失败
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: null
                    time: 1701234567
                invalid_amount:
                  summary: 支付金额无效
                  value:
                    code: 1
                    message: "支付金额必须大于0.01"
                    data: null
                    time: 1701234567
                invalid_bill_id:
                  summary: 账单ID无效
                  value:
                    code: 1
                    message: "账单ID必须大于0"
                    data: null
                    time: 1701234567
                unauthorized:
                  summary: 未授权访问
                  value:
                    code: 1
                    message: "用户信息获取失败"
                    data: null
                    time: 1701234567
                bill_not_found:
                  summary: 账单不存在
                  value:
                    code: 1
                    message: "账单不存在"
                    data: null
                    time: 1701234567
                invalid_bill_status:
                  summary: 账单状态不允许支付
                  value:
                    code: 1
                    message: "账单状态不允许部分还款，当前状态: 1"
                    data: null
                    time: 1701234567
                amount_exceeded:
                  summary: 支付金额超限
                  value:
                    code: 1
                    message: "支付金额超过剩余应还金额，剩余: 500.00，支付: 1000.50"
                    data: null
                    time: 1701234567
                order_not_found:
                  summary: 订单不存在
                  value:
                    code: 1
                    message: "订单不存在"
                    data: null
                    time: 1701234567
                database_error:
                  summary: 数据库操作失败
                  value:
                    code: 1
                    message: "创建支付流水失败: 数据库连接异常"
                    data: null
                    time: 1701234567

  /business/payment/manager/cancelOfflinePayment:
    post:
      summary: 销账取消
      description: |
        取消部分线下支付流水，逆向更新账单和订单信息。

        **功能说明：**
        1. 根据流水号查询并验证流水记录
        2. 软删除指定的支付流水记录
        3. 逆向更新账单的已还金额和状态
        4. 逆向更新订单的已还金额和状态
        5. 记录详细的操作日志便于追踪

        **业务规则：**
        - 只能取消"部分线下支付"类型的流水
        - 只能取消"成功"状态的流水
        - 取消金额不能超过账单或订单的已还金额
        - 使用账单维度内存锁防止并发操作

        **状态回滚逻辑：**
        - 账单状态根据剩余已还金额重新计算
        - 订单状态根据总已还金额重新计算
        - 如果账单完全未支付，清空paid_at字段
        - 如果订单未完全还清，清空completed_at字段

        **注意事项：**
        - 该接口需要身份验证，只有授权用户可以操作
        - 取消操作会记录操作员信息和详细日志
        - 流水记录采用软删除，不会物理删除数据
      tags:
        - 支付管理
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CancelOfflinePaymentRequest'
            example:
              transaction_no: "TXN20241201001"
      responses:
        '200':
          description: 销账取消处理完成
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    description: 响应状态码，0表示成功
                    example: 0
                  message:
                    type: string
                    description: 响应消息
                    example: "销账取消处理成功"
                  data:
                    $ref: '#/components/schemas/CancelOfflinePaymentResponse'
                  time:
                    type: integer
                    description: 响应时间戳
                    example: 1701234567
              examples:
                success:
                  summary: 取消成功
                  value:
                    code: 0
                    message: "销账取消处理成功"
                    data:
                      transaction_no: "TXN20241201001"
                      bill_id: 12345
                      amount: 500.00
                      status: "success"
                      message: "销账取消处理成功"
                    time: 1701234567
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 1
                  message:
                    type: string
                    example: "参数验证失败"
                  data:
                    type: "null"
                  time:
                    type: integer
                    example: 1701234567
              examples:
                invalid_params:
                  summary: 参数验证失败
                  value:
                    code: 1
                    message: "参数验证失败"
                    data: null
                    time: 1701234567
                transaction_not_found:
                  summary: 流水记录不存在
                  value:
                    code: 1
                    message: "流水记录不存在"
                    data: null
                    time: 1701234567
                invalid_transaction_type:
                  summary: 流水类型不匹配
                  value:
                    code: 1
                    message: "只能取消部分线下支付流水"
                    data: null
                    time: 1701234567
                invalid_transaction_status:
                  summary: 流水状态不允许取消
                  value:
                    code: 1
                    message: "只能取消成功状态的流水"
                    data: null
                    time: 1701234567
                already_cancelled:
                  summary: 流水已被取消
                  value:
                    code: 1
                    message: "流水已被取消"
                    data: null
                    time: 1701234567
                amount_exceeded:
                  summary: 取消金额超限
                  value:
                    code: 1
                    message: "取消金额超过账单已还金额"
                    data: null
                    time: 1701234567
        '401':
          description: 未授权访问
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 1
                  message:
                    type: string
                    example: "用户信息获取失败"
                  data:
                    type: "null"
                  time:
                    type: integer
                    example: 1701234567
              examples:
                unauthorized:
                  summary: 未授权访问
                  value:
                    code: 1
                    message: "用户信息获取失败"
                    data: null
                    time: 1701234567
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 1
                  message:
                    type: string
                    example: "销账取消处理失败"
                  data:
                    type: "null"
                  time:
                    type: integer
                    example: 1701234567
              examples:
                database_error:
                  summary: 数据库操作失败
                  value:
                    code: 1
                    message: "销账取消处理失败: 数据库连接异常"
                    data: null
                    time: 1701234567

  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token 认证，格式：Bearer {token}

tags:
  - name: 支付管理
    description: 支付管理相关的所有API接口，包括客户退款、代付回调通知、部分线下支付、销账取消等功能

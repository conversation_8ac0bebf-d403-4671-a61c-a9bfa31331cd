{"level":"dev.info","ts":"[2025-08-13 09:10:31.680]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.687]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.733]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-13 09:10:31.741]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-13 09:10:42.455]","caller":"results/rebjson.go:90","msg":"请求失败: msg: 获取趋势统计数据失败","data":"\"获取回款金额趋势数据失败: 查询回款金额趋势数据失败: Error 1054 (42S22): Unknown column 'deleted_at' in 'where clause'\""}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.452]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.454]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.725]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-13 09:11:33.757]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.848]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.851]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.879]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-13 10:52:52.895]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-13 10:53:38.667]","caller":"results/rebjson.go:90","msg":"请求失败: msg: 商户余额信息查询失败","data":"\"商户余额查询失败: 签名验证失败[EG000001]\""}
{"level":"dev.info","ts":"[2025-08-13 10:53:43.867]","caller":"results/rebjson.go:90","msg":"请求失败: msg: 商户余额信息查询失败","data":"\"商户余额查询失败: 签名验证失败[EG000001]\""}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.356]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.359]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.386]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-13 10:54:22.405]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.119]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.123]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.150]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-13 10:56:19.156]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.567]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.572]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.632]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-13 11:00:09.638]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.202]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.210]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.250]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-13 11:04:46.256]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.600]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.607]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.638]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-13 11:05:30.647]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.255]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.261]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.287]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-13 14:51:50.291]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.451]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.454]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.475]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-13 14:54:03.481]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.757]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.761]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.810]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-13 15:02:14.815]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.222]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.226]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.262]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-13 15:11:06.271]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.914]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.918]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.971]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-13 15:13:05.980]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.warn","ts":"[2025-08-13 15:13:08.064]","caller":"route/router.go:88","msg":"路由不存在","request_id":"185b4220b6cc1daced546e5d","method":"GET","url":"/business/payment/manager/queryMerchantsBalance","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/queryMerchantsBalance","method":"GET"}
{"level":"dev.warn","ts":"[2025-08-13 15:14:59.729]","caller":"route/router.go:88","msg":"路由不存在","request_id":"185b423ab68f7dc446484752","method":"GET","url":"/business/payment/manager/GetMerchantsBalance","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/GetMerchantsBalance","method":"GET"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.905]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.908]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.965]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-13 15:27:08.971]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-13 15:35:22.118]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-13 15:35:22.121]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-13 15:35:22.158]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-13 15:35:22.163]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.warn","ts":"[2025-08-13 15:35:24.772]","caller":"route/router.go:88","msg":"路由不存在","request_id":"185b4357f0c03cb89e4667b1","method":"GET","url":"/business/payment/manager/GetMerchantsBalance","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","path":"/business/payment/manager/GetMerchantsBalance","method":"GET"}
{"level":"dev.info","ts":"[2025-08-13 15:39:37.399]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-13 15:39:37.402]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-13 15:39:37.432]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-13 15:39:37.441]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-13 15:43:05.792]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-13 15:43:05.796]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-13 15:43:05.841]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-13 15:43:05.861]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-13 15:47:23.861]","caller":"runtime/proc.go:283","msg":"项目启动成功"}
{"level":"dev.info","ts":"[2025-08-13 15:47:23.866]","caller":"bootstrap/router.go:33","msg":"连接数据库中:1"}
{"level":"dev.info","ts":"[2025-08-13 15:47:23.944]","caller":"bootstrap/router.go:33","msg":"连接数据库成功:1"}
{"level":"dev.info","ts":"[2025-08-13 15:47:23.950]","caller":"src/main.go:58","msg":"启动端口：8108"}
{"level":"dev.info","ts":"[2025-08-13 15:47:30.297]","caller":"src/main.go:58","msg":"程序正在退出运行..."}
{"level":"dev.info","ts":"[2025-08-13 15:47:30.298]","caller":"src/main.go:58","msg":"定时任务调度器已停止"}
{"level":"dev.info","ts":"[2025-08-13 15:47:30.298]","caller":"src/main.go:58","msg":"正在关闭HTTP服务器..."}
{"level":"dev.info","ts":"[2025-08-13 15:47:30.298]","caller":"src/main.go:58","msg":"HTTP服务器已关闭"}
